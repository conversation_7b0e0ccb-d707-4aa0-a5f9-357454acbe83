@extends('layouts.admin')
@push('title', get_phrase('Dashboard'))
@push('meta')@endpush
@push('css')
    <style>
        /* Modern Dashboard Styles */
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            color: white;
            margin-bottom: 30px;
            overflow: hidden;
            position: relative;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .dashboard-header .title {
            color: white !important;
            font-weight: 600;
            font-size: 18px;
        }

        /* Enhanced Statistics Cards */
        .stats-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            height: 100%;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-gradient);
            transition: height 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stats-card:hover::before {
            height: 6px;
        }

        .stats-card.courses {
            --card-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stats-card.lessons {
            --card-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stats-card.enrollments {
            --card-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stats-card.students {
            --card-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stats-card.instructors {
            --card-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            background: var(--card-gradient);
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .stats-number {
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
            line-height: 1;
        }

        .stats-label {
            font-size: 14px;
            color: #718096;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .stats-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
            font-weight: 600;
        }

        .stats-trend.up {
            color: #48bb78;
        }

        .stats-trend.down {
            color: #f56565;
        }

        .stats-trend-icon {
            margin-right: 4px;
            font-size: 10px;
        }

        /* Enhanced Chart Container */
        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f7fafc;
        }

        .chart-title {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
            margin: 0;
        }

        .chart-subtitle {
            font-size: 14px;
            color: #718096;
            margin-top: 4px;
        }

        /* Enhanced Pie Chart Section */
        .pie-chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: 100%;
        }

        .pie-chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f7fafc;
        }

        .pie-chart-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin: 0;
        }

        .color-info-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .color-info-list li {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            transition: background-color 0.2s ease;
        }

        .color-info-list li:hover {
            background-color: #f7fafc;
        }

        .info-list-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .color-info-list .title2 {
            font-weight: 600;
            color: #4a5568;
        }

        /* Leaderboard Styles */
        .leaderboard-section {
            margin-bottom: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .medal-container {
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-bottom: 30px;
            padding: 20px 0;
            position: relative;
        }

        .medal-item {
            text-align: center;
            position: relative;
            transition: transform 0.3s ease;
            margin: 0 20px;
        }

        .medal-item:hover {
            transform: translateY(-5px);
        }

        .medal-item.first-place {
            margin-bottom: -20px;
            z-index: 3;
        }

        .medal-img {
            width: 90px;
            height: auto;
            margin: 0 auto 10px;
            transition: all 0.3s ease;
            filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.1));
        }

        .medal-username {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 5px;
            color: #444;
            max-width: 120px;
            margin: 0 auto 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .medal-score {
            font-size: 22px;
            font-weight: bold;
            color: #333;
            background: linear-gradient(135deg, #3a7bd5, #00d2ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .podium {
            display: flex;
            justify-content: center;
            align-items: flex-end;
            height: 60px;
            margin-top: 15px;
        }

        .podium-step {
            width: 90px;
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
            border-radius: 5px 5px 0 0;
            margin: 0 5px;
            box-shadow: 0 -3px 5px rgba(0, 0, 0, 0.05);
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #666;
            font-weight: bold;
            font-size: 14px;
        }

        .podium-step.gold {
            height: 60px;
            background: linear-gradient(to bottom, #fceabb, #fccd4d);
        }

        .podium-step.silver {
            height: 45px;
            background: linear-gradient(to bottom, #e0e0e0, #c0c0c0);
        }

        .podium-step.bronze {
            height: 30px;
            background: linear-gradient(to bottom, #ebc298, #cd7f32);
        }

        .leaderboard-list {
            margin-top: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            background: white;
        }

        .leaderboard-list-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 20px;
            border-bottom: 1px solid #eaeaea;
            transition: all 0.2s ease;
        }

        .leaderboard-list-item:hover {
            background-color: #f9f9f9;
            transform: translateX(5px);
        }

        .leaderboard-list-item:last-child {
            border-bottom: none;
        }

        .rank {
            font-weight: bold;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f4f4f4;
            box-shadow: inset 0 0 0 2px #e0e0e0;
        }

        .toggle-view {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 15px;
        }

        .toggle-btn {
            border: none;
            background: #f0f0f0;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 5px;
            font-weight: 500;
            color: #666;
        }

        .toggle-btn.active {
            background: #007bff;
            color: white;
            box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3);
        }

        .toggle-btn:hover:not(.active) {
            background: #e0e0e0;
        }

        .leaderboard-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .leaderboard-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-right: auto;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .stats-card {
                margin-bottom: 20px;
            }

            .chart-container,
            .pie-chart-container {
                padding: 20px;
            }

            .medal-container {
                flex-direction: column;
                align-items: center;
            }

            .medal-item {
                margin: 10px 0;
            }
        }

        /* Animation for page load */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stats-card,
        .chart-container,
        .pie-chart-container {
            animation: fadeInUp 0.6s ease-out;
        }

        .stats-card:nth-child(1) { animation-delay: 0.1s; }
        .stats-card:nth-child(2) { animation-delay: 0.2s; }
        .stats-card:nth-child(3) { animation-delay: 0.3s; }
        .stats-card:nth-child(4) { animation-delay: 0.4s; }
        .stats-card:nth-child(5) { animation-delay: 0.5s; }
    </style>
@endpush
@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-4 px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    {{ get_phrase('Dashboard') }}
                </h4>
            </div>
        </div>
    </div>

    <div class="row g-2 g-sm-3 my-3 row-cols-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5">
        <div class="col">
            <div class="ol-card card-hover">
                <div class="ol-card-body px-20px py-3">
                    <p class="title card-title-hover fs-18px my-2">{{ $statistics['course_count'] }}</p>
                    <p class="sub-title fs-14px">{{ get_phrase('Number of Courses') }}</p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="ol-card card-hover">
                <div class="ol-card-body px-20px py-3">
                    <p class="title card-title-hover fs-18px my-2">{{ $statistics['lesson_count'] }}</p>
                    <p class="sub-title fs-14px">{{ get_phrase('Number of Lessons') }}</p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="ol-card card-hover">
                <div class="ol-card-body px-20px py-3">
                    <p class="title card-title-hover fs-18px my-2">{{ $statistics['enrollment_count'] }}</p>
                    <p class="sub-title fs-14px">{{ get_phrase('Number of Enrollment') }}</p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="ol-card card-hover">
                <div class="ol-card-body px-20px py-3">
                    <p class="title card-title-hover fs-18px my-2">{{ $statistics['student_count'] }}</p>
                    <p class="sub-title fs-14px">{{ get_phrase('Number of Students') }}</p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="ol-card card-hover">
                <div class="ol-card-body px-20px py-3">
                    <p class="title card-title-hover fs-18px my-2">{{ $statistics['instructor_count'] }}</p>
                    <p class="sub-title fs-14px">{{ get_phrase('Number of Instructor') }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-12">
            <div class="ol-card p-3">
                <div class="row">
                    <div class="col-md-6">
                        <h2 class="title fs-14px">{{ get_phrase('Admin Revenue This Year') }}</h2>
                    </div>
                    <div class="col-md-6 text-end">
                        <a class="btn-link" href="{{route('admin.revenue')}}" data-bs-toggle="tooltip"
                           data-bs-placement="bottom" title="{{ get_phrase('Admin Revenue') }}"><i
                                class="fi-rr-arrow-alt-right"></i></a>
                    </div>
                </div>
                <div class="ol-card-body">
                    <canvas id="myChart" class="mw-100 w-100" height="320px"></canvas>
                </div> <!-- end card body-->
            </div> <!-- end card -->
        </div><!-- end col-->
    </div>


    <div class="row my-3">
        <div class="col-md-5">
            <div class="ol-card">
                <div class="ol-card-body p-3">
                    <div class="row">
                        <div class="col-md-6">
                            <h4 class="title fs-14px">{{get_phrase('Course Status')}}</h4>
                        </div>
                        <div class="col-md-6 text-end">
                            <a class="btn-link" href="{{route('admin.courses')}}" data-bs-toggle="tooltip"
                               data-bs-placement="bottom" title="{{ get_phrase('Explore Courses') }}"><i
                                    class="fi-rr-arrow-alt-right"></i></a>
                        </div>
                    </div>
                    <div class="d-flex align-items-center g-30px flex-wrap flex-xl-nowrap justify-content-center">
                        <div class="pie-chart-sm">
                            <canvas id="pie2"></canvas>
                        </div>
                        <div class="pie-chart-sm-details">
                            <ul class="color-info-list">
                                <li>
                                    <span class="info-list-color bg-active"></span>
                                    <span class="title2 fs-14px">{{get_phrase('Active')}}</span>
                                </li>
                                <li>
                                    <span class="info-list-color bg-upcoming"></span>
                                    <span class="title2 fs-14px">{{get_phrase('Upcoming')}}</span>
                                </li>
                                <li>
                                    <span class="info-list-color bg-pending"></span>
                                    <span class="title2 fs-14px">{{get_phrase('Pending')}}</span>
                                </li>
                                <li>
                                    <span class="info-list-color bg-private"></span>
                                    <span class="title2 fs-14px">{{get_phrase('Private')}}</span>
                                </li>
                                <li>
                                    <span class="info-list-color bg-draft"></span>
                                    <span class="title2 fs-14px">{{get_phrase('Draft')}}</span>
                                </li>
                                <li>
                                    <span class="info-list-color bg-inactive"></span>
                                    <span class="title2 fs-14px">{{get_phrase('Inactive')}}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @if(addon_check('my.affiliate'))
            <div class="col-md-7">
                <div class="ol-card">
                    <div class="ol-card-body p-3">
                        <div class="row">
                            <div class="col-md-6">
                                <h4 class="title fs-14px">{{get_phrase('Affiliate Leaderboard')}} 🏆</h4>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="toggle-view">
                                    <button class="toggle-btn active" data-view="amount">Doanh thu</button>
                                    <button class="toggle-btn" data-view="orders">Đơn hàng</button>
                                </div>
                            </div>
                        </div>

                        <div class="leaderboard-section amount-view">
                            <div class="leaderboard-header">
                                <h4 class="leaderboard-title">Top Doanh Thu</h4>
                            </div>
                            
                            <div class="medal-container">
                                @if($top_affiliates_amount->count() > 0)
                                    <div class="medal-item">
                                        <img src="{{ asset('assets/backend/images/silver-medal.png') }}"
                                            alt="Silver Medal" class="medal-img">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_amount[1]->user) ? $top_affiliates_amount[1]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_amount[1]) ? number_format($top_affiliates_amount[1]->total_amount) . ' đ' : '0 đ' }}</div>
                                    </div>
                                    
                                    <div class="medal-item first-place">
                                        <img src="{{ asset('assets/backend/images/gold-medal.png') }}" alt="Gold Medal"
                                            class="medal-img gold">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_amount[0]->user) ? $top_affiliates_amount[0]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_amount[0]) ? number_format($top_affiliates_amount[0]->total_amount) . ' đ' : '0 đ' }}</div>
                                    </div>
                                    
                                    <div class="medal-item">
                                        <img src="{{ asset('assets/backend/images/bronze-medal.png') }}"
                                            alt="Bronze Medal" class="medal-img">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_amount[2]->user) ? $top_affiliates_amount[2]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_amount[2]) ? number_format($top_affiliates_amount[2]->total_amount) . ' đ' : '0 đ' }}</div>
                                    </div>
                                @else
                                    <p class="text-center">Không có dữ liệu cộng tác viên</p>
                                @endif
                            </div>
                            
                            <div class="podium">
                                <div class="podium-step silver">2</div>
                                <div class="podium-step gold">1</div>
                                <div class="podium-step bronze">3</div>
                            </div>
                            
                            <div class="leaderboard-list">
                                @for($i = 3; $i < $top_affiliates_amount->count(); $i++)
                                    <div class="leaderboard-list-item">
                                        <div class="d-flex align-items-center">
                                            <span class="rank">{{ $i + 1 }}</span>
                                            <span class="ms-3">{{ $top_affiliates_amount[$i]->user->name }}</span>
                                        </div>
                                        <div>{{ number_format($top_affiliates_amount[$i]->total_amount) . ' đ' }}</div>
                                    </div>
                                @endfor
                            </div>
                        </div>

                        <div class="leaderboard-section orders-view" style="display: none;">
                            <div class="leaderboard-header">
                                <h4 class="leaderboard-title">Top Đơn Hàng</h4>
                            </div>
                            
                            <div class="medal-container">
                                @if($top_affiliates_orders->count() > 0)
                                    <div class="medal-item">
                                        <img src="{{ asset('assets/backend/images/silver-medal.png') }}"
                                            alt="Silver Medal" class="medal-img">

                                        <div
                                            class="medal-username">{{ isset($top_affiliates_orders[1]->user) ? $top_affiliates_orders[1]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_orders[1]) ? number_format($top_affiliates_orders[1]->total_orders) : '0' }}</div>
                                    </div>
                                    
                                    <div class="medal-item first-place">
                                        <img src="{{ asset('assets/backend/images/gold-medal.png') }}" alt="Gold Medal"
                                            class="medal-img gold">
                                        
                                        <div
                                            class="medal-username">{{ isset($top_affiliates_orders[0]->user) ? $top_affiliates_orders[0]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_orders[0]) ? number_format($top_affiliates_orders[0]->total_orders) : '0' }}</div>
                                    </div>
                                    
                                    <div class="medal-item">
                                        <img src="{{ asset('assets/backend/images/bronze-medal.png') }}"
                                            alt="Bronze Medal" class="medal-img">
                                        
                                        <div
                                            class="medal-username">{{ isset($top_affiliates_orders[2]->user) ? $top_affiliates_orders[2]->user->name : 'N/A' }}</div>
                                        <div
                                            class="medal-score">{{ isset($top_affiliates_orders[2]) ? number_format($top_affiliates_orders[2]->total_orders) : '0' }}</div>
                                    </div>
                                @else
                                    <p class="text-center">Không có dữ liệu cộng tác viên</p>
                                @endif
                            </div>
                            
                            <div class="podium">
                                <div class="podium-step silver">2</div>
                                <div class="podium-step gold">1</div>
                                <div class="podium-step bronze">3</div>
                            </div>
                            
                            <div class="leaderboard-list">
                                @for($i = 3; $i < $top_affiliates_orders->count(); $i++)
                                    <div class="leaderboard-list-item">
                                        <div class="d-flex align-items-center">
                                            <span class="rank">{{ $i + 1 }}</span>
                                            <span class="ms-3">{{ $top_affiliates_orders[$i]->user->name }}</span>
                                        </div>
                                        <div>{{ number_format($top_affiliates_orders[$i]->total_orders) }}</div>
                                    </div>
                                @endfor
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        <div class="col-md-12 mt-3">
            <div class="ol-card" id='unpaid-instructor-revenue'>
                <div class="ol-card-body p-3">
                    <div class="row">
                        <div class="col-md-6">
                            <h4 class="title text-14px mb-3">{{ get_phrase('Pending Requested withdrawal') }}</h4>
                        </div>
                        <div class="col-md-6 text-end">
                            <a class="btn-link" href="{{route('admin.affiliate.withdraws')}}" data-bs-toggle="tooltip"
                               data-bs-placement="bottom" title="{{ get_phrase('Instructor Payout') }}"><i
                                    class="fi-rr-arrow-alt-right"></i></a>
                        </div>
                    </div>
                    <div class="table-responsive purchase_list mt-4" id="purchase_list">
                        <table class="table eTable eTable-2 print-table">
                            <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">{{ get_phrase('Name') }}</th>
                                <th scope="col">{{ get_phrase('Payout amount') }}</th>
                                <th scope="col">{{ get_phrase('Request date') }}</th>
                                <th scope="col">{{ get_phrase('Bank Info') }}</th>
                                <th scope="col" class="print-d-none">{{ get_phrase('Action') }}</th>
                            </tr>
                            </thead>
                            <tbody>
                            @if ($pending_withdraws->total() > 0)
                                @foreach ($pending_withdraws as $key => $pending_withdraw)
                                    <tr>
                                        <th scope="row">
                                            <p class="row-number">{{ ++$key }}</p>
                                        </th>
                                        <td>
                                            <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                                <div class="dAdmin_profile_img">
                                                    <img class="img-fluid rounded-circle image-45" width="45"
                                                         height="45"
                                                         src="{{ get_image($pending_withdraw->user->photo) }}"/>
                                                </div>
                                                <div class="ms-1">
                                                    <h4 class="title fs-14px">
                                                        {{ $pending_withdraw->user->name }}
                                                    </h4>
                                                    <p class="sub-title2 text-12px">
                                                        {{ $pending_withdraw->user->email }}
                                                    </p>
                                                </div>
                                            </div>
                                        </td>

                                        <td>
                                            <div class="dAdmin_info_name">
                                                <p>{{ currency($pending_withdraw->amount) }}</p>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="dAdmin_info_name">
                                                <p>
                                                    @if(is_string($pending_withdraw->request_date))
                                                        {{ $pending_withdraw->request_date }}
                                                    @elseif($pending_withdraw->request_date instanceof \DateTime || $pending_withdraw->request_date instanceof \Carbon\Carbon)
                                                        {{ $pending_withdraw->request_date->format('d/m/Y') }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </p>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="dAdmin_info_name">
                                                @if(!empty($pending_withdraw->note))
                                                    <small>{!! nl2br(e($pending_withdraw->note)) !!}</small>
                                                @else
                                                    <small class="text-muted">{{ get_phrase('No bank info') }}</small>
                                                @endif
                                            </div>
                                        </td>

                                        <td class="print-d-none">
                                            <div class="d-flex gap-2">
                                                <form
                                                    action="{{ route('admin.affiliate.withdraw.status', [$pending_withdraw->id, 1]) }}"
                                                    method="post">
                                                    @csrf
                                                    <button type="submit" class="btn btn-success btn-sm">
                                                        <i class="fi-rr-check"></i>
                                                        {{ get_phrase('Completed') }}</button>
                                                </form>
                                                <form
                                                    action="{{ route('admin.affiliate.withdraw.status', [$pending_withdraw->id, 2]) }}"
                                                    method="post">
                                                    @csrf
                                                    <button type="submit" class="btn btn-danger btn-sm">
                                                        <i class="fi-rr-ban"></i>
                                                        {{ get_phrase('Canceled') }}</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <th colspan="6" class="text-center">
                                        @include('admin.no_data')
                                    </th>
                                </tr>

                            @endif
                            <tr>
                                <th></th>
                                <th></th>
                                <th>{{ get_phrase('Total') }} :
                                    {{ currency($pending_withdraws->sum('amount')) }}
                                </th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @php
        $active = $course_statistics['active'];
        $upcoming = $course_statistics['upcoming'];
        $pending = $course_statistics['pending'];
        $private = $course_statistics['private'];
        $draft = $course_statistics['draft'];
        $inactive = $course_statistics['inactive'];
    @endphp
@endsection

@push('js')

    {{-- Oliv template start --}}
    <script src="{{ asset('assets/backend/vendors/apexcharts/apexcharts.min.js') }}"></script>
    <script src="{{ asset('assets/backend/vendors/chart-js/chart.js') }}"></script>
    {{-- Oliv template end --}}


    <script>
        "use strict";

        // Format số tiền với dấu phân cách hàng nghìn
        const formatMoney = (value) => {
            return new Intl.NumberFormat('vi-VN', {
                style: 'decimal',
                maximumFractionDigits: 0
            }).format(value);
        };
        
        // Lấy dữ liệu từ controller
        const chartData = <?php echo json_encode($monthly_amount); ?>;
        
        // Định nghĩa các màu sắc chính cho các loại doanh thu
        const chartColors = {
            total: {
                backgroundColor: "rgba(54, 162, 235, 0.7)",
                borderColor: "#36a2eb",
                indicatorColor: "#36a2eb"
            },
            affiliate: {
                backgroundColor: "rgba(255, 99, 132, 0.7)",
                borderColor: "#ff6384",
                indicatorColor: "#ff6384"
            },
            actual: {
                backgroundColor: "rgba(75, 192, 192, 0.7)",
                borderColor: "#4bc0c0",
                indicatorColor: "#4bc0c0"
            }
        };
        
        // Định nghĩa external tooltip
        const getOrCreateTooltip = (chart) => {
            let tooltipEl = chart.canvas.parentNode.querySelector('div.custom-tooltip');
            
            if (!tooltipEl) {
                tooltipEl = document.createElement('div');
                tooltipEl.className = 'custom-tooltip';
                tooltipEl.style.opacity = 0;
                tooltipEl.style.pointerEvents = 'none';
                tooltipEl.style.position = 'absolute';
                tooltipEl.style.transform = 'translate(-50%, 0)';
                tooltipEl.style.transition = 'all .1s ease';
                tooltipEl.style.zIndex = 1000;
                
                const canvas = chart.canvas;
                canvas.parentNode.appendChild(tooltipEl);
            }
            
            return tooltipEl;
        };
        
        // Tạo biểu đồ với Chart.js
        const revenueChart = new Chart("myChart", {
            type: "bar",
            data: {
                labels: chartData.labels,
                datasets: [
                    {
                        label: "Tổng doanh thu",
                        backgroundColor: chartColors.total.backgroundColor,
                        borderColor: chartColors.total.borderColor,
                        borderWidth: 1,
                        data: chartData.total_revenue
                    },
                    {
                        label: "Hoa hồng affiliate",
                        backgroundColor: chartColors.affiliate.backgroundColor,
                        borderColor: chartColors.affiliate.borderColor,
                        borderWidth: 1,
                        data: chartData.affiliate_amount
                    },
                    {
                        label: "Doanh thu thực tế",
                        backgroundColor: chartColors.actual.backgroundColor,
                        borderColor: chartColors.actual.borderColor,
                        borderWidth: 1,
                        data: chartData.actual_revenue
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatMoney(value) + ' đ';
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        enabled: false,
                        external: function(context) {
                            const {chart, tooltip} = context;
                            const tooltipEl = getOrCreateTooltip(chart);
                            
                            // Ẩn tooltip nếu không có dữ liệu
                            if (tooltip.opacity === 0) {
                                tooltipEl.style.opacity = 0;
                                return;
                            }
                            
                            // Lấy dữ liệu từ tooltip
                            if (tooltip.body) {
                                const titleLines = tooltip.title || [];
                                const bodyLines = tooltip.body.map(b => b.lines);
                                
                                // Lấy index của tháng đang được hover
                                const dataIndex = context.tooltip.dataPoints[0].dataIndex;
                                
                                // Lấy dữ liệu doanh thu cho tháng đó
                                const totalRevenue = chartData.total_revenue[dataIndex];
                                const affiliateAmount = chartData.affiliate_amount[dataIndex];
                                const actualRevenue = chartData.actual_revenue[dataIndex];
                                
                                // Tính tỷ lệ hoa hồng
                                const percentage = totalRevenue > 0 
                                    ? (affiliateAmount / totalRevenue * 100).toFixed(1) 
                                    : 0;
                                
                                // Tạo HTML cho tooltip
                                const tooltipHTML = `
                                    <div style="background: #333; color: white; padding: 8px 12px; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.2); max-width: 250px; width: auto; text-align: left;">
                                        <div style="font-weight: bold; padding-bottom: 8px; border-bottom: 1px solid rgba(255,255,255,0.2); margin-bottom: 8px;">
                                            ${titleLines[0]}
                                        </div>
                                        <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background-color: ${chartColors.total.indicatorColor}; margin-right: 8px;"></div>
                                            <div>Tổng doanh thu: <strong>${formatMoney(totalRevenue)} đ</strong></div>
                                        </div>
                                        <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background-color: ${chartColors.affiliate.indicatorColor}; margin-right: 8px;"></div>
                                            <div>Hoa hồng affiliate: <strong>${formatMoney(affiliateAmount)} đ</strong></div>
                                        </div>
                                        <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background-color: ${chartColors.actual.indicatorColor}; margin-right: 8px;"></div>
                                            <div>Doanh thu thực tế: <strong>${formatMoney(actualRevenue)} đ</strong></div>
                                        </div>
                                        <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid rgba(255,255,255,0.2);">
                                            Tỷ lệ hoa hồng: <strong>${percentage}%</strong>
                                        </div>
                                    </div>
                                `;
                                
                                tooltipEl.innerHTML = tooltipHTML;
                            }
                            
                            // Cập nhật vị trí tooltip
                            const position = context.chart.canvas.getBoundingClientRect();
                            tooltipEl.style.opacity = 1;

                            // Đặt cố định ở trên cùng giữa biểu đồ
                            tooltipEl.style.left = position.left + position.width / 2 + 'px';
                            tooltipEl.style.top = position.top + 20 + 'px';
                            tooltipEl.style.transform = 'translate(-50%, 0)';

                            // Tooltip sẽ có vị trí cố định, luôn hiển thị ở trên cùng giữa biểu đồ
                        }
                    }
                },
                // Chỉ hiển thị một tooltip khi hover
                interaction: {
                    mode: 'index',
                    intersect: false
                }
            }
        });


        // Pie Chart 2
        const project_progress2 = document.getElementById('pie2');
        const progressData2 = {
            labels: ['Active', 'Upcoming', 'Pending', 'Private', 'Draft', 'Inactive'],
            data: [{{$active}}, {{$upcoming}}, {{$pending}}, {{$private}}, {{$draft}}, {{$inactive}}],
        };
        var barColors = [
            "#12c093",
            "#1b84ff",
            "#ff2583",
            "#000",
            "#878d97",
            "#dadada",
        ];
        new Chart(project_progress2, {
            type: 'doughnut',
            data: {
                labels: progressData2.labels,
                datasets: [{
                    backgroundColor: barColors,
                    label: ' {{get_phrase("Courses")}}',
                    data: progressData2.data,
                },],
            },
            options: {
                responsive: true,
                borderWidth: 5,
                hoverBorderColor: '#fff',
                plugins: {
                    legend: {
                        display: false,
                    },
                },
            },
        });

        // Toggle between amount and orders view
        document.addEventListener('DOMContentLoaded', function () {
            const toggleBtns = document.querySelectorAll('.toggle-btn');
            const amountView = document.querySelector('.amount-view');
            const ordersView = document.querySelector('.orders-view');

            toggleBtns.forEach(btn => {
                btn.addEventListener('click', function () {
                    // Remove active class from all buttons
                    toggleBtns.forEach(b => b.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');

                    // Toggle views
                    if (this.dataset.view === 'amount') {
                        amountView.style.display = 'block';
                        ordersView.style.display = 'none';
                    } else {
                        amountView.style.display = 'none';
                        ordersView.style.display = 'block';
                    }
                });
            });
        });
    </script>
@endpush
