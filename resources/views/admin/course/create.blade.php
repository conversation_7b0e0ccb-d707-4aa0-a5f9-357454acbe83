@extends('layouts.admin')
@push('title', '<PERSON><PERSON><PERSON> kh<PERSON>a học mới')

@push('css')
<style>
    .course-create-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .course-form-card {
        background: #ffffff;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .course-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
    }

    .course-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    }

    .course-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .course-header p {
        font-size: 1.1rem;
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        position: relative;
        z-index: 1;
    }

    .form-section {
        background: #ffffff;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .form-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .section-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.5rem;
        color: white;
    }

    .section-icon.basic { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .section-icon.settings { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .section-icon.pricing { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .section-icon.media { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

    .section-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
    }

    .section-subtitle {
        font-size: 0.9rem;
        color: #718096;
        margin: 0.25rem 0 0 0;
    }

    .modern-form-group {
        margin-bottom: 1.5rem;
    }

    .modern-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.95rem;
    }

    .modern-input {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: #ffffff;
    }

    .modern-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
    }

    .modern-textarea {
        min-height: 120px;
        resize: vertical;
    }

    .modern-select {
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    .radio-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-top: 0.5rem;
    }

    .radio-item {
        position: relative;
    }

    .radio-input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }

    .radio-label {
        display: block;
        padding: 0.75rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        font-weight: 500;
        background: #ffffff;
    }

    .radio-input:checked + .radio-label {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .checkbox-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .checkbox-item:hover {
        border-color: #667eea;
        background: #f7fafc;
    }

    .checkbox-input {
        margin-right: 0.75rem;
        transform: scale(1.2);
    }

    .submit-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        text-align: center;
        border-radius: 15px;
        margin-top: 2rem;
    }

    .submit-btn {
        background: #ffffff;
        color: #667eea;
        border: none;
        padding: 1rem 3rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        color: #5a67d8;
    }

    .file-upload-area {
        border: 2px dashed #e2e8f0;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .file-upload-area:hover {
        border-color: #667eea;
        background: #f7fafc;
    }

    .file-upload-icon {
        font-size: 3rem;
        color: #a0aec0;
        margin-bottom: 1rem;
    }

    .pricing-toggle {
        margin-bottom: 1.5rem;
    }

    .pricing-content {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 1rem;
        border: 1px solid #e2e8f0;
    }

    @media (max-width: 768px) {
        .course-header h1 {
            font-size: 2rem;
        }

        .form-section {
            padding: 1.5rem;
        }

        .radio-group {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush

@section('content')
<div class="course-create-container">
    <div class="container">
        <div class="course-form-card">
            <div class="course-header">
                <h1><i class="fi-rr-graduation-cap me-3"></i>Tạo khóa học mới</h1>
                <p>Tạo một khóa học chất lượng cao để chia sẻ kiến thức của bạn</p>
            </div>

            <div class="p-4">
                <form action="{{ route('admin.course.store') }}" method="post" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="course_type" value="general" required>
                    <input type="hidden" name="instructors[]" value="{{ auth()->user()->id }}" required>

                    <!-- Thông tin cơ bản -->
                    <div class="form-section">
                        <div class="section-header">
                            <div class="section-icon basic">
                                <i class="fi-rr-document"></i>
                            </div>
                            <div>
                                <h3 class="section-title">Thông tin cơ bản</h3>
                                <p class="section-subtitle">Nhập thông tin cơ bản về khóa học của bạn</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="modern-form-group">
                                    <label class="modern-label">Tên khóa học <span class="text-danger">*</span></label>
                                    <input type="text" name="title" class="modern-input" placeholder="Nhập tên khóa học" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="modern-form-group">
                                    <label class="modern-label">Danh mục <span class="text-danger">*</span></label>
                                    <select class="modern-input modern-select" name="category_id" required>
                                        <option value="">Chọn danh mục</option>
                                        @foreach (App\Models\Category::where('parent_id', 0)->orderBy('title', 'desc')->get() as $category)
                                            <option value="{{ $category->id }}">{{ $category->title }}</option>
                                            @foreach ($category->childs as $sub_category)
                                                <option value="{{ $sub_category->id }}">-- {{ $sub_category->title }}</option>
                                            @endforeach
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="modern-form-group">
                                    <label class="modern-label">Loại khóa học <span class="text-danger">*</span></label>
                                    <select class="modern-input modern-select" name="level" required>
                                        <option value="">Chọn loại khóa học</option>
                                        <option value="video">Video</option>
                                        <option value="ebook">Ebook</option>
                                        <option value="videoandzoom">Video + Zoom</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="modern-form-group">
                                    <label class="modern-label">Mô tả ngắn</label>
                                    <textarea name="short_description" class="modern-input modern-textarea" placeholder="Nhập mô tả ngắn về khóa học" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="modern-form-group">
                                    <label class="modern-label">Mô tả chi tiết</label>
                                    <textarea name="description" class="modern-input modern-textarea text_editor" placeholder="Nhập mô tả chi tiết về khóa học"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                                    <div class="fpb-7 mb-3">
                                        <label class="form-label ol-form-label" for="title">{{ get_phrase('Title') }}<span class="text-danger ms-1">*</span></label>
                                        <input type="text" name = "title" class="form-control ol-form-control" placeholder="{{ get_phrase('Enter Course Title') }}" required>
                                    </div>
                                    <div class="fpb-7 mb-3">
                                        <label class="form-label ol-form-label" for="short_description">{{ get_phrase('Short Description') }}</label>
                                        <textarea name="short_description" placeholder="{{ get_phrase('Enter Short Description') }}" class="form-control ol-form-control" rows="5"></textarea>
                                    </div>
                                    <div class="fpb-7 mb-3">
                                        <label class="form-label ol-form-label" for="description">{{ get_phrase('Description') }}</label>
                                        <textarea name="description" placeholder="{{ get_phrase('Enter Description') }}" class="form-control ol-form-control text_editor"></textarea>
                                    </div>
                                    <div class="fpb-7 mb-2 ">
                                        <label for="course_status" class="col-sm-2 col-form-label">{{ get_phrase('Create as') }}
                                            <span class="text-danger ms-1">*</span></label>
                                        <div class="eRadios">
                                            <div class="form-check">
                                                <input type="radio" value="active" name="status" class="form-check-input eRadioSuccess" id="status_active" required checked>
                                                <label for="status_active" class="form-check-label">{{ get_phrase('Active') }}</label>
                                            </div>

                                            <div class="form-check">
                                                <input type="radio" value="private" name="status" class="form-check-input eRadioPrimary" id="status_private" required>
                                                <label for="status_private" class="form-check-label">{{ get_phrase('Private') }}</label>
                                            </div>

                                            <div class="form-check">
                                                <input type="radio" value="upcoming" name="status" class="form-check-input eRadioInfo" id="status_upcoming" required>
                                                <label for="status_upcoming" class="form-check-label">{{ get_phrase('Upcoming') }}</label>
                                            </div>

                                            <div class="form-check">
                                                <input type="radio" value="pending" name="status" class="form-check-input eRadioDanger" id="status_pending" required>
                                                <label for="status_pending" class="form-check-label">{{ get_phrase('Pending') }}</label>
                                            </div>

                                            <div class="form-check">
                                                <input type="radio" value="draft" name="status" class="form-check-input eRadioSecondary" id="status_draft" required>
                                                <label for="status_draft" class="form-check-label">{{ get_phrase('Draft') }}</label>
                                            </div>

                                            <div class="form-check">
                                                <input type="radio" value="inactive" name="status" class="form-check-input eRadioDark" id="status_inactive" required>
                                                <label for="status_inactive" class="form-check-label">{{ get_phrase('Inactive') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="eForm-layouts">
                                    <div class="fpb-7 mb-3">
                                        <label for="category_id" class="form-label ol-form-label">{{ get_phrase('Category') }}<span class="text-danger ms-1">*</span></label>
                                        <select class="ol-select2" name="category_id" id="category_id" required>
                                            <option value="">{{ get_phrase('Select a category') }}</option>
                                            @foreach (App\Models\Category::where('parent_id', 0)->orderBy('title', 'desc')->get() as $category)
                                                <option value="{{ $category->id }}"> {{ $category->title }}</option>

                                                @foreach ($category->childs as $sub_category)
                                                    <option value="{{ $sub_category->id }}"> --
                                                        {{ $sub_category->title }}
                                                    </option>
                                                @endforeach
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="fpb-7 mb-3">
                                        <label for="level" class="form-label ol-form-label">{{ get_phrase('Course type') }}<span class="text-danger ms-1">*</span></label>
                                        <select class="ol-select2" name="level" id="level" required>
                                            <option value="">{{ get_phrase('Select your course type') }}</option>
                                            <option value="video">{{ get_phrase('video') }}</option>
                                            <option value="ebook">{{ get_phrase('Ebook') }}</option>
                                            <option value="videoandzoom">{{ get_phrase('Video + Zoom') }}</option>
                                        </select>
                                    </div>
                                    <div class="fpb-7 mb-3">
                                        <div class="eRadios">
                                            <div class="form-check">
                                                <input type="checkbox" name="is_best" value="1" class="form-check-input eRadioSuccess" id="is_best">
                                                <label for="is_best" class="form-check-label">Hiển thị khóa học này trên trang chủ</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="fpb-7 mb-3">
                                        <label class="form-label ol-form-label col-sm-2 col-form-label">{{ get_phrase('Pricing type') }}<span class="text-danger ms-1">*</span></label>

                                        <div class="eRadios">
                                            <div class="form-check">
                                                <input type="radio" name="is_paid" value="1" class="form-check-input eRadioSuccess" id="paid" onchange="$('#paid-section').slideDown(200)" checked>
                                                <label for="paid" class="form-check-label">{{ get_phrase('Paid') }}</label>
                                            </div>

                                            <div class="form-check">
                                                <input type="radio" name="is_paid" value="0" class="form-check-input eRadioSuccess" id="free" onchange="$('#paid-section').slideUp(200)">
                                                <label for="free" class="form-check-label">{{ get_phrase('Free') }}</label>
                                            </div>
                                            <div class="paid-section" id="paid-section">
                                                <div class="fpb-7 mb-3">
                                                    <label for="price" class="form-label ol-form-label">{{ get_phrase('Price') }}
                                                        <small>({{ currency() }})</small><span class="text-danger ms-1">*</span></label>

                                                    <input type="number" name="price" class="form-control ol-form-control" id="price" min="1" step=".01" placeholder="{{ get_phrase('Enter your course price') }} ({{ currency() }})">
                                                </div>

                                                <div class="fpb-7 mb-3">
                                                    <div class="form-check">
                                                        <input type="checkbox" name="discount_flag" value="1" class="form-check-input eRadioSuccess" id="discount_flag">
                                                        <label for="discount_flag" class="form-check-label">{{ get_phrase('Check if this course has discount') }}</label>
                                                    </div>
                                                </div>

                                                <div class="fpb-7 mb-3">
                                                    <label for="discounted_price" class="form-label ol-form-label">{{ get_phrase('Discounted price') }}</label>

                                                    <input type="number" name="discounted_price" class="form-control ol-form-control" id="discounted_price" min="1" step=".01" placeholder="{{ get_phrase('Enter your discount price') }} ({{ currency() }})">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="fpb-7 mb-3">
                                        <label class="form-label ol-form-label col-sm-2 col-form-label">{{ get_phrase('Expiry period') }}</label>
                                        <div class="eRadios">
                                            <div class="form-check mr-2">
                                                <input type="radio" id="lifetime_expiry_period" name="expiry_period" class="form-check-input eRadioSuccess" value="lifetime" onchange="checkExpiryPeriod(this)" checked>
                                                <label class="form-check-label" for="lifetime_expiry_period">{{ get_phrase('Lifetime') }}</label>
                                            </div>
                                            <div class="form-check">
                                                <input type="radio" id="limited_expiry_period" name="expiry_period" class="form-check-input eRadioSuccess" value="limited_time" onchange="checkExpiryPeriod(this)">
                                                <label class="form-check-label" for="limited_expiry_period">{{ get_phrase('Limited time') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="fpb-7 mb-3">
                                        <label class="form-label ol-form-label col-sm-2 col-form-label">{{ get_phrase('Try learning') }}</label>
                                        <div class="eRadios">
                                            <div class="form-check mr-2">
                                                <input type="radio" id="lifetime_expiry_period" name="try_learning" class="form-check-input eRadioSuccess" value="0" checked>
                                                <label class="form-check-label" for="lifetime_expiry_period">{{ get_phrase('Off') }}</label>
                                            </div>
                                            <div class="form-check">
                                                <input type="radio" id="limited_expiry_period" name="try_learning" class="form-check-input eRadioSuccess" value="1" >
                                                <label class="form-check-label" for="limited_expiry_period">{{ get_phrase('On') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="fpb-7 mb-3" id="number_of_month" style="display: none">
                                        <label class="form-label ol-form-label col-sm-3 col-form-label">{{ get_phrase('Number of month') }}</label>

                                        <input class="form-control ol-form-control" type="number" name="number_of_month" min="1" placeholder="{{ get_phrase('After purchase, students can access the course until your selected month.') }}">
                                    </div>

                                    <div class="fpb-7 mb-3 d-none">
                                        <label for="enable_drip_content" class="form-label ol-form-label col-sm-4">{{ get_phrase('Enable drip content') }}
                                            <span class="text-danger ms-1">*</span></label>
                                        <div class="eRadios">
                                            <div class="form-check">
                                                <input type="radio" value="0" name="enable_drip_content" class="form-check-input eRadioSuccess" id="drip_off" required checked>
                                                <label for="drip_off" class="form-check-label">{{ get_phrase('Off') }}</label>
                                            </div>

                                            <div class="form-check">
                                                <input type="radio" value="1" name="enable_drip_content" class="form-check-input eRadioPrimary" id="drip_on" required>
                                                <label for="drip_on" class="form-check-label">{{ get_phrase('On') }}</label>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="fpb-7">
                                    <label for="thumbnail" class="form-label ol-form-label">{{ get_phrase('Thumbnail') }}</label>
                                    <input type="file" name="thumbnail" class="form-control ol-form-control" id="thumbnail" accept="image/*" />
                                </div>
                            </div>
                            <div class="pt-2">
                                <button type="submit" class="btn ol-btn-primary float-end">{{ get_phrase('Submit') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        "use strict";

        //Start progress
        var totalSteps = $('#v-pills-tab .nav-link').length
        var progressVal = 100 / totalSteps;
        $(function() {
            var pValPerItem = progressVal;
            $('#courseFormProgress .progress-bar').attr('aria-valuemin', 0);
            $('#courseFormProgress .progress-bar').attr('aria-valuemax', pValPerItem);
            $('#courseFormProgress .progress-bar').attr('aria-valuenow', pValPerItem);
            $('#courseFormProgress .progress-bar').width(pValPerItem + '%');
            $('#courseFormProgress .progress-bar').text("Step 1 out of " + totalSteps);
        });

        $("#v-pills-tab .nav-link").on('click', function() {
            var currentStep = $("#v-pills-tab .nav-link").index(this) + 1;
            var pValPerItem = currentStep * progressVal;
            $('#courseFormProgress .progress-bar').attr('aria-valuemin', 0);
            $('#courseFormProgress .progress-bar').attr('aria-valuemax', pValPerItem);
            $('#courseFormProgress .progress-bar').attr('aria-valuenow', pValPerItem);
            $('#courseFormProgress .progress-bar').width(pValPerItem + '%');
            $('#courseFormProgress .progress-bar').text("Step " + currentStep + " out of " + totalSteps);

            if (currentStep == totalSteps) {
                $('#courseFormProgress .progress-bar').text("{{ get_phrase('Finish!') }}");
                $('#courseFormProgress .progress-bar').addClass('bg-success');
            } else {
                $('#courseFormProgress .progress-bar').removeClass('bg-success');
            }
        });
        //End progress

        function checkExpiryPeriod(e) {
            var expiryPeriod = $(e).val();
            if (expiryPeriod == 'lifetime') {
                $('#number_of_month').slideUp();
            } else {
                $('#number_of_month').slideDown();
            }
        }
    </script>
@endpush
