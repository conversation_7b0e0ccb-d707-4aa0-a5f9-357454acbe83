@php
    $builder = App\Models\Builder_page::where('id', $course_details->builder_ids)->firstOrNew();
@endphp
@extends('layouts.default')

@push('title', $course_details->title)
@push('meta')@endpush
@push('css')@endpush
@section('content')

    @if($course_details->builder_ids)
        @if($builder->is_permanent)

            @include('components.home_permanent_templates.'.$builder->identifier)
        @endif
        @php $builder_files = $builder->html ? json_decode($builder->html, true) : [];  @endphp

        @foreach ($builder_files as $builder_file_name)
            @include('components.home_made_by_builder.'.$builder_file_name)
        @endforeach
    @else
        @include('frontend.default.course.course_details_default')
    @endif
@endsection
