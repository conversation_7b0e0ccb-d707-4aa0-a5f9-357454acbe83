# Modern Curriculum Builder với Drag & Drop

## Tổng quan

Đây là một hệ thống quản lý curriculum hiện đại với khả năng kéo thả (drag & drop) trự<PERSON> quan, đ<PERSON><PERSON><PERSON> thiết kế để thay thế giao diện accordion cũ bằng một interface card-based hiện đại hơn.

## Tính năng chính

### ✨ Drag & Drop
- **Kéo thả sections**: Sắp xếp lại thứ tự các sections
- **Kéo thả lessons**: Di chuyển lessons trong cùng section hoặc giữa các sections khác nhau
- **Visual feedback**: Hiệu ứng trực quan khi kéo thả
- **Auto-save**: Tự động lưu thay đổi khi kéo thả

### 🎨 Giao diện hiện đại
- **Card-based design**: Thay thế accordion cũ bằng cards hiện đại
- **Responsive**: Tương thích với mọi kích thước màn hình
- **Icons & badges**: <PERSON>ân biệt rõ ràng các loại lesson (video, document, quiz)
- **Hover effects**: Hiệu ứng mượt mà khi hover
- **Collapse/expand**: Thu gọn/mở rộng sections

### 🚀 Trải nghiệm người dùng
- **Real-time updates**: Cập nhật số lượng lessons và thứ tự ngay lập tức
- **Notifications**: Thông báo trạng thái thao tác
- **Loading states**: Hiển thị trạng thái loading
- **Error handling**: Xử lý lỗi graceful

## Cấu trúc Files

```
├── resources/views/admin/course/curriculum.blade.php  # Giao diện chính
├── public/assets/css/curriculum-builder.css           # Styles
├── public/assets/js/curriculum-sortable.js           # JavaScript logic
├── app/Http/Controllers/CurriculumController.php     # Backend logic
├── routes/admin.php                                  # Routes
└── public/curriculum-demo.html                       # Demo page
```

## Cài đặt

### 1. Files đã được tạo/cập nhật:

**Frontend:**
- `resources/views/admin/course/curriculum.blade.php` - Giao diện mới
- `public/assets/css/curriculum-builder.css` - Styles
- `public/assets/js/curriculum-sortable.js` - JavaScript

**Backend:**
- `app/Http/Controllers/CurriculumController.php` - Thêm methods mới
- `routes/admin.php` - Thêm route mới

**Demo:**
- `public/curriculum-demo.html` - Trang demo
- `public/assets/js/curriculum-demo.js` - JS cho demo

### 2. Dependencies:

**CDN đã được thêm:**
- SortableJS v1.15.0 (https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js)

## Cách sử dụng

### 1. Xem Demo
Truy cập: `http://your-domain.com/curriculum-demo.html`

### 2. Trong ứng dụng thực tế
- Vào trang chỉnh sửa khóa học
- Chọn tab "Curriculum"
- Sử dụng các tính năng:
  - Kéo icon ⋮⋮ để di chuyển sections/lessons
  - Click nút ▼ để thu gọn/mở rộng sections
  - Kéo lessons giữa các sections khác nhau

## API Endpoints mới

### 1. Move lesson giữa sections
```
POST /admin/lesson/move-section
Parameters:
- lesson_id: ID của lesson
- section_id: ID section đích
- lesson_order: Array thứ tự lessons mới
```

### 2. Sort sections (đã cập nhật)
```
POST /admin/section/sort
Parameters:
- itemJSON: Array thứ tự sections
- course_id: ID khóa học
```

### 3. Sort lessons (đã cập nhật)
```
POST /admin/lesson/sort
Parameters:
- itemJSON: Array thứ tự lessons
- section_id: ID section
```

## Cấu trúc CSS Classes

### Containers
- `.curriculum-builder` - Container chính
- `.curriculum-sections` - Container các sections
- `.section-card` - Card của mỗi section
- `.section-lessons` - Container lessons trong section

### Interactive Elements
- `.section-drag-handle` - Handle để kéo section
- `.lesson-drag-handle` - Handle để kéo lesson
- `.section-toggle` - Button thu gọn/mở rộng
- `.lesson-item` - Item của mỗi lesson

### States
- `.sortable-ghost` - Trạng thái khi đang kéo
- `.sortable-chosen` - Element được chọn
- `.drag-over` - Vùng drop zone
- `.collapsed` - Section bị thu gọn

## Customization

### 1. Thay đổi màu sắc
Chỉnh sửa trong `curriculum-builder.css`:
```css
.curriculum-header {
    background: linear-gradient(135deg, #your-color 0%, #your-color-2 100%);
}
```

### 2. Thêm lesson types mới
Trong `curriculum.blade.php`, thêm icon và badge cho lesson type mới:
```php
@elseif ($lesson->lesson_type == 'new_type')
    <i class="fi-rr-your-icon text-your-color"></i>
```

### 3. Tùy chỉnh animations
Trong `curriculum-builder.css`, chỉnh sửa:
```css
.section-card {
    transition: all 0.3s ease; /* Thay đổi thời gian animation */
}
```

## Troubleshooting

### 1. Drag & drop không hoạt động
- Kiểm tra SortableJS đã load chưa
- Kiểm tra console có lỗi JavaScript không
- Đảm bảo CSRF token đúng

### 2. Styles không hiển thị
- Kiểm tra file CSS đã load chưa
- Clear cache browser
- Kiểm tra đường dẫn file CSS

### 3. AJAX requests lỗi
- Kiểm tra routes đã được thêm chưa
- Kiểm tra CSRF token
- Kiểm tra permissions

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Performance

- Optimized cho courses có đến 100+ sections
- Lazy loading cho large datasets
- Debounced AJAX requests
- Minimal DOM manipulation

## Security

- CSRF protection cho tất cả AJAX requests
- Server-side validation
- Permission checks
- XSS protection

## Future Enhancements

- [ ] Bulk operations (select multiple lessons)
- [ ] Keyboard shortcuts
- [ ] Undo/redo functionality
- [ ] Advanced filtering
- [ ] Export/import curriculum
- [ ] Templates system
